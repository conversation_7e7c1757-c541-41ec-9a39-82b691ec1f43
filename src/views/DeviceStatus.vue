<template>
  <div class="device-status-container">
    <div class="page-header">
      <h1>设备状态监控</h1>
      <div class="filter-area">
        <div class="current-selection">
          <el-tag size="large" type="info">
            <el-icon><Shop /></el-icon>
            {{ globalStore.storeDisplayName }}
          </el-tag>
          <el-tag size="large" type="success" style="margin-left: 10px">
            <el-icon><Calendar /></el-icon>
            {{ globalStore.selectedDate }}
          </el-tag>
        </div>
        <div class="selection-hint">
          <el-text type="info" size="small">
            请在Dashboard页面选择门店和日期
          </el-text>
        </div>
      </div>
    </div>

    <!-- 设备状态概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">总设备数</div>
          <div class="card-value">{{ deviceData.totalDevices }}</div>
          <div class="card-footer">
            <el-tag size="small">人工POS: {{ deviceData.manualCount }}</el-tag>
            <el-tag size="small" type="success"
              >自助POS: {{ deviceData.selfServiceCount }}</el-tag
            >
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">在线设备</div>
          <div class="card-value">{{ deviceData.onlineDevices }}</div>
          <div class="card-footer">
            <el-progress
              :percentage="
                Math.round(
                  (deviceData.onlineDevices / deviceData.totalDevices) * 100
                )
              "
              :stroke-width="6"
              status="success"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">工作中设备</div>
          <div class="card-value">{{ deviceData.activeDevices }}</div>
          <div class="card-footer">
            <el-progress
              :percentage="
                Math.round(
                  (deviceData.activeDevices / deviceData.onlineDevices) * 100
                )
              "
              :stroke-width="6"
              color="#409EFF"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="status-card">
          <div class="card-title">异常设备</div>
          <div class="card-value">{{ deviceData.abnormalDevices }}</div>
          <div class="card-footer">
            <el-tag
              size="small"
              type="danger"
              v-if="deviceData.abnormalDevices > 0"
            >
              需要关注
            </el-tag>
            <el-tag size="small" type="success" v-else> 一切正常 </el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备状态列表 -->
    <el-card shadow="hover" class="device-table-card">
      <template #header>
        <div class="card-header">
          <span>设备状态列表</span>
          <div class="header-actions">
            <el-radio-group v-model="deviceTypeFilter" size="small">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="manual">人工POS</el-radio-button>
              <el-radio-button label="selfService">自助POS</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <el-table :data="filteredDevices" style="width: 100%">
        <el-table-column prop="deviceId" label="设备ID" width="120" />
        <el-table-column prop="type" label="设备类型">
          <template #default="scope">
            {{ scope.row.type === "manual" ? "人工POS" : "自助POS" }}
          </template>
        </el-table-column>
        <el-table-column prop="location" label="位置" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastActiveTime" label="最后活跃时间" />
        <el-table-column prop="runningTime" label="今日运行时长" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="handleDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="handleRemoteControl(scope.row)"
              :disabled="scope.row.status === 'offline'"
            >
              远程控制
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { getDeviceStatusData, getStoreList } from "@/api";
import { useGlobalStore } from "@/stores/global";

// 使用全局状态管理
const globalStore = useGlobalStore();

// 本地状态
const deviceData = ref({
  totalDevices: 0,
  onlineDevices: 0,
  activeDevices: 0,
  abnormalDevices: 0,
  manualCount: 0,
  selfServiceCount: 0,
  devices: [],
});
const deviceTypeFilter = ref("all");

// 计算属性
const filteredDevices = computed(() => {
  if (deviceTypeFilter.value === "all") {
    return deviceData.value.devices;
  }
  return deviceData.value.devices.filter(
    (device) => device.type === deviceTypeFilter.value
  );
});

// 方法
const getStatusTagType = (status) => {
  const types = {
    active: "success",
    idle: "info",
    offline: "danger",
    abnormal: "warning",
  };
  return types[status] || "info";
};

const getStatusText = (status) => {
  const texts = {
    active: "使用中",
    idle: "空闲",
    offline: "离线",
    abnormal: "异常",
  };
  return texts[status] || status;
};

const handleDetail = (device) => {
  console.log("查看设备详情", device);
  // 实现查看设备详情的逻辑
};

const handleRemoteControl = (device) => {
  console.log("远程控制设备", device);
  // 实现远程控制设备的逻辑
};

// 获取数据的函数
const fetchDeviceStatusData = async () => {
  if (!globalStore.selectedStoreId) return;

  try {
    console.log(
      `设备状态页面：获取门店 ${globalStore.selectedStoreId} 在 ${globalStore.selectedDate} 的数据`
    );
    const data = await getDeviceStatusData({
      storeId: globalStore.selectedStoreId,
      date: globalStore.selectedDate,
    });
    deviceData.value = data;
    console.log("设备状态数据获取成功:", data);
  } catch (error) {
    console.error("获取设备状态数据失败:", error);
  }
};

// 生命周期
onMounted(async () => {
  try {
    // 获取初始数据
    await fetchDeviceStatusData();
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
});

// 监听全局状态变化，重新获取数据
watch(
  [() => globalStore.selectedStoreId, () => globalStore.selectedDate],
  async () => {
    console.log("设备状态页面：检测到全局状态变化，重新获取数据");
    await fetchDeviceStatusData();
  }
);
</script>

<style lang="scss" scoped>
.device-status-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }

    .filter-area {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .current-selection {
        display: flex;
        align-items: center;
      }

      .selection-hint {
        opacity: 0.7;
      }
    }
  }

  .status-card {
    height: 180px;

    .card-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .card-value {
      font-size: 28px;
      font-weight: 500;
      margin-bottom: 20px;
    }

    .card-footer {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .device-table-card {
    margin-top: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
